# 🤖 Auto Register Tool - 13win

Tool tự động đăng ký tài khoản và nhận thưởng cho 13win với tính năng xuất dữ liệu lên GitHub.

## ✨ Tính năng chính

### 🚀 Đăng ký tự động
- **Đăng ký hàng loạt:** Tạo nhiều tài khoản cùng lúc
- **Chrome riêng biệt:** Mỗi tài khoản sử dụng 1 Chrome instance
- **Tự động nhận thưởng:** Auto claim 14k bonus sau khi đăng ký
- **Xử lý popup:** Tự động xử lý popup hồng bao

### 📊 Quản lý tài khoản
- **Danh sách tài khoản:** Hiển thị tất cả tài khoản đã tạo
- **Trạng thái chi tiết:** Theo dõi trạng thái đăng ký và thưởng
- **Xuất file:** TXT và Excel format
- **Xuất lên GitHub:** Chia sẻ dữ liệu trực tiếp

### 🗂️ Tổ chức file
- **accounts_data/**: Lưu dữ liệu tài khoản
- **accounts_data/exports/**: File xuất TXT và Excel
- **logs/**: Log files theo ngày

## 🚀 Cài đặt

### 1. Cài đặt Python packages
```bash
pip install selenium requests openpyxl
```

### 2. Chạy tool
```bash
python run_simple.py
```

## 📖 Hướng dẫn sử dụng

### 🔧 Cấu hình cơ bản
1. **URL:** Nhập link 13win (mặc định có sẵn)
2. **Số lượng tài khoản:** Chọn số tài khoản muốn tạo
3. **Thông tin:** Tùy chọn username, password, họ tên

### 🤖 Đăng ký tự động
1. Click **"🚀 Bắt đầu đăng ký tự động"**
2. Tool sẽ tự động:
   - Khởi động Chrome cho mỗi tài khoản
   - Truy cập website
   - Điền form đăng ký
   - Xử lý popup hồng bao
   - Nhận thưởng 14k
   - Cập nhật trạng thái

### 📋 Xem danh sách tài khoản
- Chuyển sang tab **"Danh sách tài khoản"**
- Xem tất cả tài khoản đã tạo
- Thống kê thành công/thất bại

### 📤 Xuất dữ liệu

#### 📄 Xuất file local
- Click **"📤 Xuất file"**
- Tạo file TXT và Excel trong folder `accounts_data/exports/`

#### 🚀 Xuất lên GitHub
1. Click **"🚀 Xuất lên GitHub"**
2. Nhập thông tin:
   - **GitHub Token:** Personal Access Token
   - **Repository:** username/repo-name
   - **Format:** TXT, Excel, hoặc cả hai
   - **Commit message:** Tự động tạo
3. Click **"🚀 Xuất lên GitHub"**

## 🔑 Cấu hình GitHub

### Tạo Personal Access Token
1. Vào GitHub → Settings → Developer settings → Personal access tokens
2. Click **"Generate new token"**
3. Chọn quyền **"repo"**
4. Copy token và dán vào tool

### Tạo Repository
1. Tạo repository mới trên GitHub
2. Nhập tên repo dạng: `username/repo-name`

## 📁 Cấu trúc thư mục

```
📁 tool/
├── 📄 simple_auto_register.py    # Main tool
├── 📄 simple_chrome_tool.py      # Chrome driver manager
├── 📄 run_simple.py              # Launcher
├── 📄 start_simple.bat           # Windows launcher
├── 📄 requirements.txt           # Dependencies
├── 📁 accounts_data/             # Dữ liệu tài khoản
│   ├── 📄 registered_accounts.json
│   └── 📁 exports/               # File xuất
│       ├── 📄 accounts_export_20240115_143025.txt
│       └── 📊 accounts_export_20240115_143025.xlsx
└── 📁 logs/                      # Log files
    └── 📄 auto_register_20240115.log
```

## 🎯 Workflow hoàn chỉnh

```
🚀 Khởi động tool
📝 Cấu hình số lượng tài khoản
🤖 Bắt đầu đăng ký tự động
   ├── 🌐 Chrome 1: Đăng ký tài khoản 1
   ├── 🌐 Chrome 2: Đăng ký tài khoản 2
   └── 🌐 Chrome N: Đăng ký tài khoản N
📋 Xem danh sách tài khoản
📤 Xuất file hoặc GitHub
🎉 Hoàn thành!
```

## ⚠️ Lưu ý

- **Chrome instances:** Mỗi tài khoản sử dụng Chrome riêng
- **Popup handling:** Tool tự động xử lý popup hồng bao
- **File organization:** Tự động dọn dẹp và sắp xếp file
- **Error handling:** Xử lý lỗi và retry thông minh

## 🛠️ Troubleshooting

### Chrome không khởi động
- Kiểm tra ChromeDriver version
- Tool tự động download ChromeDriver 137

### Không tìm thấy form đăng ký
- Kiểm tra URL website
- Sử dụng Debug Form để phân tích

### Lỗi xuất GitHub
- Kiểm tra GitHub Token
- Đảm bảo repository tồn tại
- Kiểm tra quyền "repo"

## 📞 Hỗ trợ

Nếu gặp vấn đề, kiểm tra:
1. **Log files** trong folder `logs/`
2. **Console output** khi chạy tool
3. **Chrome browser** có mở đúng website không

---

**🎮 Happy Gaming! 🎰**
